.custom-checkbox {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  user-select: none;
  
  // 默认状态
  &:hover {
    transform: scale(1.05);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  }

  &:active {
    transform: scale(0.95);
  }

  // 选中状态
  &--checked {
    &:hover {
      box-shadow: 0 6px 16px rgba(126, 187, 176, 0.4);
    }
  }

  // 禁用状态
  &--disabled {
    cursor: not-allowed;
    opacity: 0.5;
    
    &:hover {
      transform: none;
      box-shadow: none;
    }

    &:active {
      transform: none;
    }
  }

  // 添加一个微妙的内阴影效果
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    border-radius: inherit;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.2) 0%, rgba(255, 255, 255, 0) 50%);
    pointer-events: none;
  }

  // 图标动画
  .anticon {
    transition: all 0.2s ease-in-out;
    
    &.anticon-plus {
      transform: rotate(0deg);
    }
    
    &.anticon-check {
      transform: rotate(0deg) scale(1);
      animation: checkAnimation 0.3s ease-in-out;
    }
  }
}

// 选中动画
@keyframes checkAnimation {
  0% {
    transform: rotate(0deg) scale(0.5);
    opacity: 0;
  }
  50% {
    transform: rotate(0deg) scale(1.2);
    opacity: 0.8;
  }
  100% {
    transform: rotate(0deg) scale(1);
    opacity: 1;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .custom-checkbox {
    &:hover {
      transform: none;
      box-shadow: none;
    }
    
    &:active {
      transform: scale(0.95);
    }
  }
}
