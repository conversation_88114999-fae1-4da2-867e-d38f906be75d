import React from 'react';
import classNames from 'classnames';
import { Add, Check } from '@nutui/icons-react-taro';
import './index.scss';

export interface CheckboxProps {
  /** 是否选中 */
  checked?: boolean;
  /** 是否禁用 */
  disabled?: boolean;
  /** 点击回调 */
  onChange?: (checked: boolean) => void;
  /** 自定义样式类名 */
  className?: string;
  /** 自定义样式 */
  style?: React.CSSProperties;
  /** 尺寸 */
  size?: 'small' | 'medium' | 'large';
  /** 主题色 */
  color?: string;
  /** 圆角大小 */
  borderRadius?: number;
}

const Checkbox: React.FC<CheckboxProps> = ({
  checked = false,
  disabled = false,
  onChange,
  className,
  style,
  size = 'medium',
  color = '#77C2C7',
  borderRadius = 8,
}) => {
  const handleClick = () => {
    if (disabled) return;
    onChange?.(!checked);
  };

  const sizeMap = {
    small: 20,
    medium: 32,
    large: 48,
  };

  const iconSizeMap = {
    small: 10,
    medium: 16,
    large: 24,
  };

  const checkboxSize = sizeMap[size];
  const iconSize = iconSizeMap[size];

  const checkboxStyle: React.CSSProperties = {
    width: checkboxSize,
    height: checkboxSize,
    borderRadius,
    backgroundColor: checked ? color : '#f5f5f5',
    border: checked ? `2px solid ${color}` : '2px solid #e0e0e0',
    ...style,
  };

  return (
    <div
      className={classNames(
        'custom-checkbox',
        {
          'custom-checkbox--checked': checked,
          'custom-checkbox--disabled': disabled,
        },
        className
      )}
      style={checkboxStyle}
      onClick={handleClick}
    >
      {checked ? (
        <Check
          style={{
            fontSize: iconSize,
            color: '#ffffff',
          }}
        />
      ) : (
        <Add
          style={{
            fontSize: iconSize,
            color: '#999999',
          }}
        />
      )}
    </div>
  );
};

export default Checkbox;
