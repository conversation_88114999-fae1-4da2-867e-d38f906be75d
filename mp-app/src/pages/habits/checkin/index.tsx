import React, { useState, useEffect } from 'react';
import { View, Text } from '@tarojs/components';
import { Button, Toast } from '@nutui/nutui-react-taro';
import Taro, { useRouter } from '@tarojs/taro';
import dayjs from 'dayjs';
import { checkinHabit, getHabitDetail, getHabitsStatistics } from '../../../services/habits';
import type { CheckinHabitParams } from '../../habits/types';
import './index.scss';

interface CheckinPageProps {}

interface HabitItem {
  id: string;
  name: string;
  icon: string;
  theme_color: string;
  description: string;
  frequency: number[];
  preferred_time?: string;
  status: 'active' | 'archived';
  created_at: string;
  updated_at: string;
}

interface HabitStatistics {
  current_streak: number;
  current_month_data: Array<{
    date: string;
    is_completed: boolean;
  }>;
}

const CheckinPage: React.FC<CheckinPageProps> = () => {
  const router = useRouter();
  const { id } = router.params;

  const [habit, setHabit] = useState<HabitItem | null>(null);
  const [habitStatistics, setHabitStatistics] = useState<HabitStatistics | null>(null);
  const [currentTime, setCurrentTime] = useState(dayjs());
  const [checkinType, setCheckinType] = useState<'time' | 'point' | 'none'>('time');
  const [loading, setLoading] = useState(false);
  const [isCheckedIn, setIsCheckedIn] = useState(false);

  useEffect(() => {
    if (id) {
      loadHabitData();
    }
    
    // 更新时间
    const timer = setInterval(() => {
      setCurrentTime(dayjs());
    }, 1000);

    return () => clearInterval(timer);
  }, [id]);

  const loadHabitData = async () => {
    try {
      const [habitData, statisticsData] = await Promise.all([
        getHabitDetail(id),
        getHabitsStatistics(id)
      ]);
      
      setHabit(habitData);
      setHabitStatistics(statisticsData);
      
      // 检查今天是否已经打卡
      const today = dayjs().format('YYYY-MM-DD');
      const todayRecord = statisticsData?.current_month_data?.find(
        item => dayjs(item.date).format('YYYY-MM-DD') === today
      );
      setIsCheckedIn(todayRecord?.is_completed || false);
    } catch (error) {
      console.error('加载习惯数据失败:', error);
      Toast.show({ content: '加载数据失败' });
    }
  };

  const handleCheckin = async () => {
    if (!id || isCheckedIn) return;

    setLoading(true);
    try {
      const checkinParams: CheckinHabitParams = {
        habit_id: id,
        check_in_date: dayjs().format('YYYY-MM-DD'),
        // notes: selectedMood ? selectedMood : ''
      };

      // 根据打卡类型添加时间信息
      if (checkinType === 'time') {
        checkinParams.check_in_time = currentTime.format('HH:mm:ss');
      } else if (checkinType === 'point' && habit?.preferred_time) {
        checkinParams.check_in_time = habit.preferred_time;
      }

      await checkinHabit(checkinParams);

      Toast.show({ content: '打卡成功！' });
      setIsCheckedIn(true);

      // 重新加载数据
      await loadHabitData();

      // 延迟返回上一页
      setTimeout(() => {
        Taro.navigateBack();
      }, 1500);
    } catch (error) {
      console.error('打卡失败:', error);
      Toast.show({ content: '打卡失败，请重试' });
    } finally {
      setLoading(false);
    }
  };

  const formatTime = (time: dayjs.Dayjs) => {
    return time.format('HH:mm');
  };

  const getClockRotation = (time: dayjs.Dayjs) => {
    const hours = time.hour() % 12;
    const minutes = time.minute();
    const hourDegree = (hours * 30) + (minutes * 0.5); // 时针角度
    const minuteDegree = minutes * 6; // 分针角度
    
    return { hourDegree, minuteDegree };
  };

  const { hourDegree, minuteDegree } = getClockRotation(currentTime);

  if (!habit) {
    return (
      <View className="checkin-page loading">
        <Text>加载中...</Text>
      </View>
    );
  }

  return (
    <View className="checkin-page">
      {/* 时钟显示 */}
      <View className="clock-container">
        <View className="clock">
          <View className="clock-face">
            {/* 时钟数字 */}
            {[12, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11].map((num, index) => (
              <View 
                key={num} 
                className="clock-number"
                style={{
                  transform: `rotate(${index * 30}deg) translateY(-80px) rotate(-${index * 30}deg)`
                }}
              >
                {num}
              </View>
            ))}
            
            {/* 时针 */}
            <View 
              className="clock-hand hour-hand"
              style={{ transform: `rotate(${hourDegree}deg)` }}
            />
            
            {/* 分针 */}
            <View 
              className="clock-hand minute-hand"
              style={{ transform: `rotate(${minuteDegree}deg)` }}
            />
            
            {/* 中心点 */}
            <View className="clock-center" />
          </View>
        </View>
        
        {/* 数字时间显示 */}
        <Text className="digital-time">{formatTime(currentTime)}</Text>
      </View>

   
      {/* 脚印动画和连续天数 */}
      <View className="achievement-section">
        <View className="footprint-animation">
          <View className="footprint-container">
            <View className="footprint main-footprint">👣</View>
            {/* 装饰性元素 */}
            <View className="decoration-dots">
              <View className="dot dot-1">✨</View>
              <View className="dot dot-2">⭐</View>
              <View className="dot dot-3">💫</View>
              <View className="dot dot-4">🌟</View>
              <View className="dot dot-5">✨</View>
              <View className="dot dot-6">⭐</View>
            </View>
          </View>
        </View>
        
        <Text className="streak-text">
          真棒！坚持第{habitStatistics?.current_streak || 1}天
        </Text>
      </View>

    

      {/* 完成打卡按钮 */}
      <View className="checkin-button-container">
        <Button
          className="checkin-button"
          type="primary"
          loading={loading}
          disabled={isCheckedIn}
          onClick={handleCheckin}
        >
          {isCheckedIn ? '今日已打卡' : '完成打卡'}
        </Button>
      </View>
    </View>
  );
};

export default CheckinPage;
