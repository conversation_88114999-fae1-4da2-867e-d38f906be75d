# 打卡页面

## 功能概述

打卡页面是一个完整的习惯打卡界面，根据设计图实现了以下功能：

- 实时时钟显示（模拟时钟 + 数字时间）
- 打卡时间选项（打卡时间、时间点、不记录时间）
- 脚印动画和连续天数显示
- 心情选择
- 完成打卡按钮

## 页面结构

### 1. 时钟组件
- **模拟时钟**：显示当前时间的时针和分针
- **数字时间**：显示当前时间的数字格式（HH:mm）
- **实时更新**：每秒更新一次时间显示

### 2. 打卡选项
- **打卡时间**：记录当前时间作为打卡时间
- **时间点**：使用习惯的偏好时间作为打卡时间
- **不记录时间**：不记录具体的打卡时间

### 3. 成就展示
- **脚印动画**：带有弹跳动画的脚印图标
- **装饰元素**：围绕脚印的星星和闪光动画
- **连续天数**：显示当前连续打卡天数

### 4. 心情选择
- **四种心情**：戚戚、有点累、表现不错、很开心
- **表情符号**：每种心情对应不同的 emoji
- **可选功能**：用户可以选择当前心情，会记录到打卡备注中

### 5. 打卡按钮
- **状态管理**：根据是否已打卡显示不同状态
- **加载状态**：打卡过程中显示加载动画
- **成功反馈**：打卡成功后显示提示并自动返回

## 接口集成

### checkinHabit 接口
```typescript
interface CheckinHabitParams {
  habit_id: string;
  check_in_date: string; // YYYY-MM-DD 格式
  notes?: string;
  check_in_time?: string; // HH:mm:ss 格式，可选
}
```

### 调用逻辑
1. 根据选择的打卡类型决定是否传递 `check_in_time`
2. 如果选择了心情，将心情文本作为 `notes` 传递
3. 打卡成功后重新加载习惯数据
4. 延迟 1.5 秒后自动返回上一页

## 页面路由

### 路由配置
页面已添加到 `app.config.ts` 中：
```typescript
pages: [
  // ...其他页面
  'pages/checkin/index',
]
```

### 跳转方式
从习惯详情页面跳转：
```typescript
Taro.navigateTo({
  url: `/pages/checkin/index?id=${habitId}`,
})
```

## 样式特点

### 1. 主题色彩
- 使用统一的主题色 `#7EBBB0`（青松绿）
- 背景色为浅灰色 `#F5F5F5`
- 白色卡片背景

### 2. 动画效果
- **弹跳动画**：脚印图标的上下弹跳
- **浮动动画**：装饰元素的旋转和移动
- **过渡动画**：按钮和选项的状态切换

### 3. 响应式设计
- 适配小程序屏幕尺寸
- 使用 rpx 单位确保不同设备的一致性
- 合理的间距和布局

## 测试指南

### 1. 功能测试
1. **页面加载**：确保页面正常加载，显示习惯信息
2. **时钟显示**：验证时钟是否正确显示当前时间
3. **选项切换**：测试三种打卡选项的切换
4. **心情选择**：测试心情选项的选择和取消
5. **打卡功能**：测试打卡按钮的点击和接口调用

### 2. 状态测试
1. **未打卡状态**：按钮显示"完成打卡"，可以点击
2. **已打卡状态**：按钮显示"今日已打卡"，禁用状态
3. **加载状态**：打卡过程中显示加载动画

### 3. 错误处理
1. **网络错误**：测试网络请求失败的处理
2. **参数错误**：测试缺少必要参数的处理
3. **重复打卡**：测试重复打卡的防护

## 技术实现

### 1. 状态管理
- 使用 React Hooks 管理组件状态
- 实时时间更新使用 `setInterval`
- 页面卸载时清理定时器

### 2. 类型安全
- 完整的 TypeScript 类型定义
- 接口参数类型检查
- 组件 Props 类型约束

### 3. 错误处理
- Try-catch 包装异步操作
- 用户友好的错误提示
- 加载状态的正确管理

## 注意事项

1. **时间同步**：确保设备时间准确，影响打卡时间记录
2. **网络状态**：打卡需要网络连接，注意网络异常处理
3. **重复打卡**：已实现防重复打卡逻辑
4. **数据刷新**：打卡成功后会重新加载数据，确保状态同步
