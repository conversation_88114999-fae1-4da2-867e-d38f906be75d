$theme-color: #7EBBB0;
$background-color: #F5F5F5;
$white: #FFFFFF;
$text-primary: #333333;
$text-secondary: #666666;
$text-light: #999999;

.checkin-page {
  min-height: 100vh;
  background-color: $background-color;
  padding: 40rpx;
  display: flex;
  flex-direction: column;
  align-items: center;

  &.loading {
    justify-content: center;
  }

  // 时钟容器
  .clock-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-bottom: 60rpx;

    .clock {
      width: 320rpx;
      height: 320rpx;
      position: relative;
      margin-bottom: 30rpx;

      .clock-face {
        width: 100%;
        height: 100%;
        border: 6rpx solid $theme-color;
        border-radius: 50%;
        background-color: $white;
        position: relative;
        box-shadow: 0 8rpx 24rpx rgba(126, 187, 176, 0.2);

        .clock-number {
          position: absolute;
          top: 50%;
          left: 50%;
          width: 40rpx;
          height: 40rpx;
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 28rpx;
          font-weight: 500;
          color: $text-secondary;
          transform-origin: 0 0;
        }

        .clock-hand {
          position: absolute;
          top: 50%;
          left: 50%;
          transform-origin: 0 0;
          background-color: $theme-color;
          border-radius: 4rpx;

          &.hour-hand {
            width: 6rpx;
            height: 80rpx;
            margin-top: -80rpx;
            margin-left: -3rpx;
          }

          &.minute-hand {
            width: 4rpx;
            height: 100rpx;
            margin-top: -100rpx;
            margin-left: -2rpx;
          }
        }

        .clock-center {
          position: absolute;
          top: 50%;
          left: 50%;
          width: 16rpx;
          height: 16rpx;
          background-color: $theme-color;
          border-radius: 50%;
          transform: translate(-50%, -50%);
          z-index: 10;
        }
      }
    }

    .digital-time {
      font-size: 48rpx;
      font-weight: 700;
      color: $text-primary;
      letter-spacing: 2rpx;
    }
  }

  // 打卡选项
  .checkin-options {
    display: flex;
    justify-content: center;
    gap: 60rpx;
    margin-bottom: 80rpx;

    .option {
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 16rpx;
      cursor: pointer;
      transition: all 0.3s ease;

      .option-dot {
        width: 24rpx;
        height: 24rpx;
        border: 4rpx solid #CCCCCC;
        border-radius: 50%;
        transition: all 0.3s ease;
      }

      .option-text {
        font-size: 28rpx;
        color: $text-secondary;
        transition: color 0.3s ease;
      }

      &.active {
        .option-dot {
          background-color: $theme-color;
          border-color: $theme-color;
        }

        .option-text {
          color: $theme-color;
          font-weight: 500;
        }
      }
    }
  }

  // 成就区域
  .achievement-section {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-bottom: 60rpx;

    .footprint-animation {
      margin-bottom: 40rpx;

      .footprint-container {
        position: relative;
        width: 200rpx;
        height: 200rpx;
        display: flex;
        align-items: center;
        justify-content: center;

        .main-footprint {
          font-size: 120rpx;
          animation: bounce 2s infinite;
        }

        .decoration-dots {
          position: absolute;
          width: 100%;
          height: 100%;

          .dot {
            position: absolute;
            font-size: 32rpx;
            animation: float 3s ease-in-out infinite;

            &.dot-1 {
              top: 20rpx;
              left: 40rpx;
              animation-delay: 0s;
            }

            &.dot-2 {
              top: 40rpx;
              right: 30rpx;
              animation-delay: 0.5s;
            }

            &.dot-3 {
              bottom: 60rpx;
              left: 20rpx;
              animation-delay: 1s;
            }

            &.dot-4 {
              bottom: 40rpx;
              right: 40rpx;
              animation-delay: 1.5s;
            }

            &.dot-5 {
              top: 80rpx;
              left: 10rpx;
              animation-delay: 2s;
            }

            &.dot-6 {
              bottom: 80rpx;
              right: 10rpx;
              animation-delay: 2.5s;
            }
          }
        }
      }
    }

    .streak-text {
      font-size: 36rpx;
      font-weight: 700;
      color: $text-primary;
      text-align: center;
    }
  }

  // 心情选择
  .mood-section {
    width: 100%;
    margin-bottom: 80rpx;

    .mood-title {
      font-size: 32rpx;
      color: $text-primary;
      text-align: center;
      margin-bottom: 40rpx;
    }

    .mood-options {
      display: flex;
      justify-content: space-between;
      gap: 20rpx;

      .mood-option {
        flex: 1;
        display: flex;
        flex-direction: column;
        align-items: center;
        padding: 20rpx 10rpx;
        background-color: $white;
        border-radius: 20rpx;
        border: 2rpx solid transparent;
        cursor: pointer;
        transition: all 0.3s ease;

        .mood-emoji {
          font-size: 32rpx;
          margin-bottom: 8rpx;
        }

        .mood-label {
          font-size: 24rpx;
          color: $text-secondary;
          text-align: center;
          line-height: 1.2;
        }

        &.selected {
          border-color: $theme-color;
          background-color: rgba(126, 187, 176, 0.1);

          .mood-label {
            color: $theme-color;
            font-weight: 500;
          }
        }

        &:hover {
          transform: translateY(-4rpx);
          box-shadow: 0 8rpx 16rpx rgba(0, 0, 0, 0.1);
        }
      }
    }
  }

  // 打卡按钮
  .checkin-button-container {
    width: 100%;
    margin-top: auto;

    .checkin-button {
      width: 100%;
      height: 100rpx;
      background: linear-gradient(135deg, $theme-color 0%, #6BA8A0 100%);
      border: none;
      border-radius: 50rpx;
      font-size: 36rpx;
      font-weight: 600;
      color: $white;
      box-shadow: 0 8rpx 24rpx rgba(126, 187, 176, 0.3);
      transition: all 0.3s ease;

      &:not(:disabled):hover {
        transform: translateY(-2rpx);
        box-shadow: 0 12rpx 32rpx rgba(126, 187, 176, 0.4);
      }

      &:disabled {
        background: #CCCCCC;
        box-shadow: none;
        cursor: not-allowed;
      }
    }
  }
}

// 动画定义
@keyframes bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-20rpx);
  }
  60% {
    transform: translateY(-10rpx);
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0) rotate(0deg);
    opacity: 0.6;
  }
  50% {
    transform: translateY(-20rpx) rotate(180deg);
    opacity: 1;
  }
}
