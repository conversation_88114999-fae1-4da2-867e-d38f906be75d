// 习惯模板信息
export interface HabitTemplateInfoDto {
  id: string;
  name: string;
  icon: string;
  theme_color: string;
  description: string;
}

// 子分类树形结构
export interface HabitSubCategoryTreeDto {
  id: string;
  name: string;
  icon: string;
  level: number;
  sort_order: number;
  templates: HabitTemplateInfoDto[];
}

// 一级分类树形结构
export interface HabitCategoryTreeDto {
  id: string;
  name: string;
  icon: string;
  level: number;
  sort_order: number;
  children: HabitSubCategoryTreeDto[];
}

// 习惯库树形结构响应（被全局拦截器包装）
export interface HabitLibraryTreeResponse {
  success: boolean;
  data: HabitCategoryTreeDto[];
}

// 创建习惯参数
export interface CreateHabitParams {
  name: string;
  icon: string;
  theme_color: string;
  description: string;
  frequency: number[];
  preferred_time?: string;
  status: 'active' | 'archived';
  template_id?: string;
}

// 习惯详情
export interface HabitDetail {
  id: string;
  name: string;
  icon: string;
  theme_color: string;
  description: string;
  frequency: number[];
  preferred_time?: string;
  status: 'active' | 'archived';
  created_at: string;
  updated_at: string;
}

// 打卡参数
export interface CheckinHabitParams {
  habit_id: string;
  check_in_date: string; // YYYY-MM-DD 格式
  notes?: string;
  check_in_time?: string; // HH:mm:ss 格式，可选
}
