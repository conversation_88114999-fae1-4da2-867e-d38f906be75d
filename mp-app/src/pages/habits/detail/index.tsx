import { Button, View } from "@tarojs/components";
import { useRouter } from "@tarojs/taro";
import { useEffect, useState } from "react"
import { CheckinCalendar } from "src/components";
import { getHabitDetail, getHabitsStatistics } from "src/services/habits"
import { Image } from '@nutui/nutui-react-taro';
import './index.scss'
import Taro from "@tarojs/taro";
import { Alarm } from '@nutui/icons-react-taro'

interface HabitItem {
  id: string;
  name: string;
  icon: string;
  theme_color: string;
  description: string;
  frequency: number[];
  preferred_time?: string;
  status: 'active' | 'archived';
  created_at: string;
  updated_at: string;
}

interface HabitStatistics {
  current_streak: number;
  current_month_data: Array<{
    date: string;
    is_completed: boolean;
  }>;
}

const HabitDetail = () => {
  const router = useRouter();
  const { id } = router.params;

  const [habit, setHabit] = useState<HabitItem>()
  const [habitStatistics, setHabitStatistics] = useState<HabitStatistics>()

  useEffect(() => {
    if (id) {
      getHabitDetail(id).then(setHabit)
      getHabitsStatistics(id).then(setHabitStatistics)
    }
  }, [id])


  const handleDayClick = (check_in_date: string) => {
    console.log('打卡日期', check_in_date)
    // 跳转到打卡页面
    Taro.navigateTo({
      url: `/pages/habits/checkin/index?id=${id}`,
    })
  };

  const handleCheckin = () => {
    // 跳转到打卡页面
    Taro.navigateTo({
      url: `/pages/habits/checkin/index?id=${id}`,
    })
  };

  return <View className='habit-detail-page'>
    <View className="card">
      <View className="habit-info">
        {habit?.icon && <Image
            src={require(`../../../assets/habits/${habit?.icon}.png`)}
            mode="aspectFit"
            width={50}
            height={50}
          />}
        <View className="habit-info-content ml-2">
          <View className="text-lg">{habit?.name}</View>
          <View className="text-gray-600 text-sm">{habit?.description}</View>
        </View>
      </View>
      {habit?.preferred_time  && <div className="mt-2 text-gray-600 text-sm flex items-center">
        <Alarm  size={
          18
        }/> 
        <span className="ml-2">提醒时间：{habit?.preferred_time}</span>
      </div>}
    </View>
    <View className="card">
      <CheckinCalendar
        data={habitStatistics?.current_month_data ?? []}
        title=""
        onDayClick={handleDayClick}
        showStats={false}
      />
    </View>

  
   

    <div className="flex justify-between gap-2">
      <Button className="flex-1" type="primary"
       onClick={handleCheckin}>立即打卡</Button>
      <Button
        className="flex-1"
        onClick={() => {
          Taro.navigateTo({
            url: `/pages/habits/create/index?id=${id}`,
          })
        }}
      >编辑习惯</Button>
    </div>
  </View>
}

export default HabitDetail