import request from '../utils/request';
import type {
  CreateHabitParams,
  CheckinHabitParams
} from '../pages/habits/types';

// 获取习惯库树形结构
export const getHabitsLibraryTree = () => {
  return request({
    url: '/habits/library/tree',
    method: 'GET',
  });
};

// 获取习惯库（旧接口，保持兼容）
export const getHabitsLibrary = () => {
  return request({
    url: '/habits/library/categories/tree',
    method: 'GET',
  });
};

export const getHabitTemplate = (id: string) => {
  return request({
    url: `/habits/library/template/${id}`,
    method: 'GET',
  }).then(res => res.data);
};

export const createHabit = (params: CreateHabitParams) => {
  return request({
    url: '/habits/my-habits',
    method: 'POST',
    data: params,
  });
};

export const getHomeHabitsOverview = () => {
  return request({
    url: '/habits/statistics/overview',
    method: 'GET',
  });
};

export const getHabitDetail = (id: string) => {
  return request({
    url: `/habits/my-habits/${id}`,
    method: 'GET',
  }).then(res => res.data);
};

export const getHabitsStatistics = (id?: string) => {
  return request({
    url: `/habits/statistics/habit/${id}`,
    method: 'GET',
  }).then(res => res.data);
};

export const checkinHabit = (params: CheckinHabitParams) => {
  return request({
    url: `/habits/check-in`,
    method: 'POST',
    data: params,
  });
};